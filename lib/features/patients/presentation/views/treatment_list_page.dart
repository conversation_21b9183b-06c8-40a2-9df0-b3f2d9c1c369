import 'package:dento_support/core/configs/colors.dart';
import 'package:dento_support/features/patients/models/patient.dart';
import 'package:dento_support/features/patients/models/treatment.dart';
import 'package:dento_support/features/patients/presentation/bloc/patient_bloc.dart';
import 'package:dento_support/features/patients/presentation/cubit/medical_cubit.dart';
import 'package:dento_support/features/patients/presentation/cubit/treatment_cubit.dart';
import 'package:dento_support/features/patients/presentation/views/editable_text.dart';
import 'package:dento_support/features/patients/presentation/views/medical_history_page.dart';
import 'package:dento_support/features/patients/presentation/widgets/common_treatement_textfield.dart';
import 'package:dento_support/features/patients/repository/patient_repository.dart';
import 'package:dento_support/injector.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class NoLeadingZeroFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    String newText = newValue.text;

    if (newText.isEmpty) {
      return newValue;
    }

    if (newText.isNotEmpty && newText.replaceAll('0', '').isEmpty && newText.length > 1) {
      return oldValue;
    }

    if (newText.length > 1 && newText.startsWith('0')) {
      newText = newText.replaceFirst(RegExp(r'^0+'), '');
      if (newText.isEmpty) {
        newText = '0';
      }

      return TextEditingValue(
        text: newText,
        selection: TextSelection.collapsed(offset: newText.length),
      );
    }

    return newValue;
  }
}

class TreatmentListPage extends StatefulWidget {
  const TreatmentListPage({
    super.key,
    // required this.treatments,
    required this.patient,
    required this.patientState,
  });

  // final List<Treatment> treatments;
  final Patient patient;
  final PatientState patientState;

  @override
  State<TreatmentListPage> createState() => _TreatmentListPageState();
}

class _TreatmentListPageState extends State<TreatmentListPage> {
  bool _showAddTreatmentForm = false;
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _priceController = TextEditingController();
  final TextEditingController _discountController = TextEditingController();
  final FocusNode _nameFocusNode = FocusNode();

  double _currentDiscountAmount = 0.0;
  String _originalDiscountAmount = '';

  @override
  void dispose() {
    _nameController.dispose();
    _priceController.dispose();
    _nameFocusNode.dispose();
    _discountController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _currentDiscountAmount = widget.patientState.discountAmount.toDouble();
    _originalDiscountAmount = widget.patientState.discountAmount.toString();
    _discountController.text = widget.patientState.discountAmount.toString();
  }

  @override
  void didUpdateWidget(TreatmentListPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.patientState.discountAmount != widget.patientState.discountAmount) {
      _currentDiscountAmount = widget.patientState.discountAmount.toDouble();
      _originalDiscountAmount = widget.patientState.discountAmount.toString();
      _discountController.text = widget.patientState.discountAmount.toString();
    }
  }
  void _toggleAddTreatmentForm() {
    setState(() {
      _showAddTreatmentForm = !_showAddTreatmentForm;
      if (!_showAddTreatmentForm) {
        _nameController.clear();
        _priceController.clear();
      }
    });
  }

  double _calculateFinalAmount(PatientState state) {
    return state.totalPayment - _currentDiscountAmount;
  }

  void _updateDiscountAmount(String value) {
    setState(() {
      _currentDiscountAmount = double.tryParse(value) ?? 0.0;
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<TreatmentCubit, TreatmentState>(
      listener: (context, state) {
        if (state.status.isSuccess) {
          FocusScope.of(context).unfocus();

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.errorMessage),
              duration: const Duration(seconds: 1),
            ),
          );
          context.read<PatientBloc>().add(FetchPatient(widget.patient.id));
          // Navigator.pop(context, true);
        }

        if (state.status.isDeleteSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.errorMessage),
              duration: const Duration(seconds: 1),
            ),
          );
          context.read<PatientBloc>().add(FetchPatient(widget.patient.id));
        }
        if (state.status.isDiscountSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.errorMessage),
              duration: const Duration(seconds: 1),
            ),
          );
        }


        if (state.status.isEditSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.errorMessage),
              duration: const Duration(seconds: 1),
            ),
          );
        }

        if (state.status.hasError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.errorMessage),
              duration: const Duration(seconds: 1),
            ),
          );
        }
      },
      child: BlocBuilder<TreatmentCubit, TreatmentState>(
        builder: (context,state) {
          return Scaffold(
            backgroundColor: Colors.white,
            body: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildPreviousMedicalHistory(
                  context,
                  onPressed: () {
                    Navigator.push<void>(
                      context,
                      MaterialPageRoute(
                        builder: (_) => BlocProvider(
                          create: (context) =>
                              MedicalCubit(patientRepository: getIt()),
                          child: MedicalHistoryPage(patient: widget.patient),
                        ),
                      ),
                    );
                  },
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 28).copyWith(top: 13),
                  child: Text("Treatment List",
                      style: AppFontStyle.style4
                          .copyWith(fontSize: 18, color: AppColor.textColor)),
                ),
                Expanded(
                  child: _buildTreatmentList(widget.patientState,state),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
  Widget _buildPreviousMedicalHistory(
      BuildContext context, {
        required void Function()? onPressed,
      }) {
    return Padding(
      padding: const EdgeInsets.only(top: 15,),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          StreamBuilder(
            stream: getIt<PatientRepository>().medicalHistories,
            builder: (_, __) {
              return Flexible(
                child: Text(
                  getIt<PatientRepository>().medicalHistoryLength == 0
                      ? 'No previous medical history.'
                      : getIt<PatientRepository>().medicalHistoriesString,
                  maxLines: 1,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontFamily: AppFont.inter,
                    fontWeight: getIt<PatientRepository>().medicalHistoryLength == 0 ? FontWeight.w500 : FontWeight.w600,
                    fontSize: 14,
                    color: getIt<PatientRepository>().medicalHistoryLength == 0 ? Color(0xFF979797) : Colors.black,
                  ),
                ),
              );
            },
          ),
          SizedBox(width: 5,),
          InkWell(
            onTap: onPressed,
            child: Icon(
              Icons.edit,
              color: AppColor.primaryColor,
              size: 15,
            ),
          ),
          // IconButton(
          //   onPressed: onPressed,
          //   padding: EdgeInsets.zero,
          //   iconSize: 14,
          //   constraints: const BoxConstraints(),
          //   icon: const Icon(
          //     Icons.edit,
          //     color: AppColor.primaryColor,
          //   ),
          // ),
        ],
      ),
    );
  }

  Widget _buildTreatmentList(PatientState state,TreatmentState treatmentState) {
    return ListView.separated(
      // shrinkWrap: true,
      padding: const EdgeInsets.symmetric(horizontal: 28),
      itemCount: treatmentState.treatmentList.length + 1,
      separatorBuilder: (context, index) => const SizedBox(),
      itemBuilder: (itemContext, index) {
        if (index == treatmentState.treatmentList.length) {
          return Column(
            children: [
              if (_showAddTreatmentForm)
                AddTreatmentForm(
                  nameController: _nameController,
                  nameFocusNode: _nameFocusNode,
                  priceController: _priceController,
                  // priceFocusNode: _priceFocusNode,
                  onToggle: _toggleAddTreatmentForm,
                  onSubmit: (name, price) async {
                    await context.read<TreatmentCubit>().addTreatment(
                        treatmentCost: price, treatmentTitle: name);
                  },
                ),
              _buildBottomSection(state,treatmentState),
            ],
          );
        }
        final treatment = treatmentState.treatmentList[index];
        return Column(
          children: [
            _buildTreatmentItem(treatment),
            Divider(),
          ],
        );
      },
    );
  }

  Widget _buildTreatmentItem(Treatment treatment) {
    return Row(
      // mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Row(
            children: [
              InkWell(
                child: Image.asset(
                  AppAssets.removeTreatment,
                  height: 16,
                  width: 16,
                ),
                onTap: () async {
                  _showAlertDialog(context, treatment);
                },
              ),
              const SizedBox(width: 12),
              Expanded(
                child: EditableTreatmentText(
                  text: treatment.name,
                  treatmentId: treatment.id,
                  onChange: (text) {}, patientId: widget.patient.id,
                ),
              ),
            ],
          ),
        ),
        EditableTreatmentPrice(
          text: '₹${treatment.amount}',
          padding: 9,
          treatmentId: treatment.id,
          style: AppFontStyle.style4.copyWith(color: AppColor.textColor),
          onChange: (text) {}, patientId: widget.patient.id,
        ),
      ],
    );
  }

  void _showAlertDialog(BuildContext context, Treatment treatment) {
    final treatmentCubit = context.read<TreatmentCubit>();
    showCupertinoModalPopup<void>(
      context: context,
      builder: (BuildContext dialogContext) =>
          CupertinoAlertDialog(
            title: const Text('Delete Treatment'),
            content: Text('Are you sure?'),
            actions: <CupertinoDialogAction>[
              CupertinoDialogAction(
                /// This parameter indicates this action is the default,
                /// and turns the action's text to bold text.
                isDefaultAction: true,
                onPressed: () {
                  Navigator.pop(dialogContext);
                },
                child: const Text('Cancel'),
              ),
              CupertinoDialogAction(
                /// This parameter indicates the action would perform
                /// a destructive action such as deletion, and turns
                /// the action's text color to red.
                isDestructiveAction: true,
                onPressed: () async {
                  Navigator.pop(dialogContext);
                  await treatmentCubit.deleteTreatment(id: treatment.id);
                },
                child: const Text('Delete'),
              ),
            ],
          ),
    );
  }


  Widget _buildBottomSection(PatientState state,TreatmentState treatmentState) {
    return Container(
      // padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          if (!_showAddTreatmentForm) ...[
            const SizedBox(height: 20),
            InkWell(
              onTap: _toggleAddTreatmentForm,
              child: Row(
                children: [
                  Image.asset(
                    AppAssets.addTreatment,
                    height: 16,
                    width: 16,
                  ),
                  const SizedBox(width: 13),
                  const Text(
                    'Add Treatment',
                    style: AppFontStyle.style7,
                  ),
                ],
              ),
            ),
          ],
          SizedBox(height: !_showAddTreatmentForm ? 15 : 5),
          Divider(),
          if (treatmentState.treatmentList.isNotEmpty) ...[
            SizedBox(height: 25),
            _buildTotalSection(state),
          ]
        ],
      ),
    );
  }

  Widget _buildTotalSection(PatientState state) {
    final total = state.totalPayment;
    final finalAmount = _calculateFinalAmount(state);

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 12),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: IntrinsicHeight(
          child: Row(
        children: [
          Expanded(child: _buildAmountColumn(label: 'Total', value: '₹${state.totalPayment.toInt()}')),
          _verticalDivider(),
          Expanded(child: _buildDiscountColumn(state)),
          _verticalDivider(),
          Expanded(
            child: _buildAmountColumn(
              label: 'Final',
              value: '₹${finalAmount.toInt()}',
              valueColor: AppColor.primaryColor,
            ),
          ),
        ],
      )),
    );
  }

  Widget _verticalDivider() {
    return Container(
      width: 1,
      height: double.infinity,
      color: const Color(0xFFE5E7EB),
      margin: const EdgeInsets.symmetric(horizontal: 12),
    );
  }

  Widget _buildAmountColumn(
      {required String label, required String value, Color? valueColor}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          label,
          style: AppFontStyle.style6,
        ),
        const SizedBox(height: 20),
        Text(
          value,
          style: TextStyle(
            fontFamily: AppFont.inter,
            fontWeight: FontWeight.w600,
            fontSize: 16,
            color: valueColor ?? AppColor.textColor,
          ),
        ),
        const SizedBox(height: 9),
      ],
    );
  }

  Widget _buildDiscountColumn(PatientState state) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          'Discount',
          style: AppFontStyle.style6,
        ),
        const SizedBox(height: 15),
        Container(
          width: 80,
          height: 35,
          padding: const EdgeInsets.symmetric(horizontal: 8),
          decoration: BoxDecoration(
            border: Border.all(color: const Color(0xFFE5E7EB)),
            borderRadius: BorderRadius.circular(8),
            color: Colors.white,
          ),
          child: Center(
            child: TextFormField(
              // initialValue: state.discountAmount.toInt().toString(),
              controller: _discountController,
              textAlign: TextAlign.center,
              keyboardType: TextInputType.numberWithOptions(decimal: false),
              // maxLines: 1,
              inputFormatters: [
                LengthLimitingTextInputFormatter(4),
                FilteringTextInputFormatter.digitsOnly,
                NoLeadingZeroFormatter(),
              ],
              onTapOutside: (event) async {
                final currentValue = _discountController.text.trim();
                // Only call API if discount amount has actually changed
                if (currentValue != _originalDiscountAmount) {
                  await context.read<TreatmentCubit>().editPatientDiscount(
                    currentValue,
                    widget.patient.id.toString(),
                  );
                  // Update local discount amount and original value after successful API call
                  _updateDiscountAmount(currentValue);
                  _originalDiscountAmount = currentValue;
                }
                FocusScope.of(context).unfocus();
              },
              onFieldSubmitted: (value) async {
                final currentValue = _discountController.text.trim();
                // Only call API if discount amount has actually changed
                if (currentValue != _originalDiscountAmount) {
                  await context.read<TreatmentCubit>().editPatientDiscount(
                    currentValue,
                    widget.patient.id.toString(),
                  );
                  // Update local discount amount and original value after successful API call
                  _updateDiscountAmount(currentValue);
                  _originalDiscountAmount = currentValue;
                }
                FocusScope.of(context).unfocus();
              },
              decoration: const InputDecoration(
                border: InputBorder.none,
                prefixText: '₹',
                prefixStyle: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
                isCollapsed: true,
              ),
              style: const TextStyle(
                fontFamily: AppFont.inter,
                fontWeight: FontWeight.w600,
                fontSize: 16,
                color: AppColor.textColor,
              ),
              onChanged: (value) {
                _updateDiscountAmount(value);
              },
            ),
          ),
        ),
      ],
    );
  }
}
