import 'package:dento_support/core/configs/colors.dart';
import 'package:dento_support/features/patients/presentation/views/new_payment_history_page.dart';
import 'package:dento_support/features/patients/presentation/views/treatment_list_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AddTreatmentForm extends StatefulWidget {
  const AddTreatmentForm({super.key,
    required this.nameController,
    required this.priceController,
    required this.nameFocusNode,
    // required this.priceFocusNode,
    required this.onToggle,
    required this.onSubmit});

  final TextEditingController nameController;
  final TextEditingController priceController;
  final FocusNode nameFocusNode;

  // final FocusNode priceFocusNode;
  final void Function() onToggle;
  final void Function(String name, String price) onSubmit;

  @override
  State<AddTreatmentForm> createState() => _AddTreatmentFormState();
}

class _AddTreatmentFormState extends State<AddTreatmentForm> {
  FocusNode priceFocusNode = FocusNode();
  bool _hasEnteredAmount = false;
  GlobalKey _closeButtonKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    widget.priceController.addListener(_onPriceChanged);
  }

  @override
  void dispose() {
    widget.priceController.removeListener(_onPriceChanged);
    priceFocusNode.dispose();
    super.dispose();
  }

  void _onPriceChanged() {
    setState(() {
      _hasEnteredAmount = widget.priceController.text.trim().isNotEmpty;
    });
  }

  void _closeViaCrossButton() {
    print("onClosed ==> Tapped");
    widget.onToggle();
  }
  bool _isTapOnCloseButton(Offset globalPosition) {
    if (_closeButtonKey.currentContext == null) return false;

    final RenderBox? renderBox =
    _closeButtonKey.currentContext!.findRenderObject() as RenderBox?;
    if (renderBox == null) return false;

    final Offset position = renderBox.localToGlobal(Offset.zero);
    final Size size = renderBox.size;

    return globalPosition.dx >= position.dx &&
        globalPosition.dx <= position.dx + size.width &&
        globalPosition.dy >= position.dy &&
        globalPosition.dy <= position.dy + size.height;
  }

  void _handleTapOutside(PointerDownEvent event) {
    if (_isTapOnCloseButton(event.position)) {
      return;
    }

    if (widget.nameController.text.trim().isNotEmpty) {
      widget.onSubmit(widget.nameController.text, widget.priceController.text);
    } else {
      widget.onToggle();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(left: 16, top: 15),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: TextField(
              autofocus: true,
              controller: widget.nameController,
              focusNode: widget.nameFocusNode,
              textInputAction: TextInputAction.next,
              textCapitalization: TextCapitalization.sentences,
              decoration: InputDecoration(
                hintText: 'Enter Name ',
                hintStyle:
                AppFontStyle.style6.copyWith(color: Color(0xFF6B7280)),
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(vertical: 8),
              ),
              onTapOutside: _handleTapOutside,
              onSubmitted: (value) {
                // When name field is submitted, focus the amount field
                if (value.trim().isNotEmpty) {
                  priceFocusNode.requestFocus();
                }
              },
              style: AppFontStyle.style6,
            ),
          ),
          const SizedBox(width: 8),
          ValueListenableBuilder(
            valueListenable: widget.nameController,
            builder: (context, value, child) {
              return Container(
                width: 80,
                height: 38,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border.all(color: Color(0xFFE5E7EB), width: 1),
                    borderRadius: BorderRadius.circular(8)),
                padding: EdgeInsets.symmetric(horizontal: 10).copyWith(bottom: 2),
                child: TextField(
                  controller: widget.priceController,
                  focusNode: priceFocusNode,
                  textAlign: TextAlign.center,
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(6),
                    FilteringTextInputFormatter.digitsOnly,
                    NoLeadingZeroFormatter(),
                  ],
                  readOnly: widget.nameController.text.trim().isEmpty,
                  keyboardType: TextInputType.number,
                  cursorColor: AppColor.textColor,
                  decoration: InputDecoration(
                    hintText: '₹0',
                    border: InputBorder.none,
                    hintStyle: AppFontStyle.style6.copyWith(color: Color(0xFF9CA3AF)),
                  ),
                  onTapOutside: _handleTapOutside,
                  onSubmitted: (value) {
                    if (widget.nameController.text.trim().isNotEmpty) {
                      widget.onSubmit(widget.nameController.text, widget.priceController.text);
                    } else {
                      widget.onToggle();
                    }
                  },
                  maxLines: 1,
                  cursorHeight: 20,
                ),
              );
            },
          ),
          InkWell(
            key: _closeButtonKey,
            onTap: _closeViaCrossButton,
            child: Container(
              padding: const EdgeInsets.all(14),
              child: const Icon(Icons.close, color: AppColor.textColor),
            ),
          ),
        ],
      ),
    );
  }
}